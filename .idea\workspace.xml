<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="导出时，草稿路径存在就覆盖">
      <change afterPath="$PROJECT_DIR$/MCP Document/1_Introduction.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/2_Architecture Overview.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/3_mcp python sdk.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/4_mcp server and client.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/MCP Document/5_mcp study.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/draft_content.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/draft_meta_info.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/material/audio.mp3" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/data/output/剪映制作示例视频/material/video.mp4" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/jianying/export.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/jianying/export.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/server.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/audio_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/audio_tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/draft_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/draft_tool.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jianyingdraft/tool/track_tool.py" beforeDir="false" afterPath="$PROJECT_DIR$/jianyingdraft/tool/track_tool.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="31GaUk5isUZ2riHe1n2owt57WVR" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.debug.executor&quot;: &quot;Run&quot;,
    &quot;Python.export.executor&quot;: &quot;Debug&quot;,
    &quot;Python.fastapi_server.executor&quot;: &quot;Debug&quot;,
    &quot;Python.server.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/pythonProject/MyProject/jianying-mcp/data/output&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\jianying-mcp\data\output" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp\jianyingdraft\zip" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp\MCP Document" />
      <recent name="D:\pythonProject\MyProject\jianying-mcp" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\pythonProject\MyProject\jianying-mcp\data" />
    </key>
  </component>
  <component name="RunManager" selected="Python.audio_tool">
    <configuration name="audio_tool" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft/tool" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/tool/audio_tool.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="debug" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/debug.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="export" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft/jianying" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/jianying/export.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="fastapi_server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/fastapi_server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="jianying-mcp" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/jianyingdraft" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/jianyingdraft/server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.audio_tool" />
        <item itemvalue="Python.fastapi_server" />
        <item itemvalue="Python.server" />
        <item itemvalue="Python.export" />
        <item itemvalue="Python.debug" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.18034.82" />
        <option value="bundled-python-sdk-975db3bf15a3-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2c6a2a9b-d3ee-44db-ba6e-31e4630b964e" name="更改" comment="" />
      <created>1755152881347</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755152881347</updated>
      <workItem from="1755152882429" duration="12635000" />
      <workItem from="1755169457162" duration="8000" />
      <workItem from="1755223507122" duration="966000" />
      <workItem from="1755244726780" duration="6746000" />
      <workItem from="1755252992595" duration="3552000" />
      <workItem from="1755483316585" duration="11000" />
      <workItem from="1755509994386" duration="3696000" />
      <workItem from="1755671119823" duration="182000" />
      <workItem from="1755686538798" duration="732000" />
    </task>
    <task id="LOCAL-00001" summary="第一次提交">
      <option name="closed" value="true" />
      <created>1755158000197</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755158000197</updated>
    </task>
    <task id="LOCAL-00002" summary="mcp基本完成">
      <option name="closed" value="true" />
      <created>1755161992088</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755161992088</updated>
    </task>
    <task id="LOCAL-00003" summary="sse">
      <option name="closed" value="true" />
      <created>1755244767634</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755244767634</updated>
    </task>
    <task id="LOCAL-00004" summary="修改端口以及导出路径问题">
      <option name="closed" value="true" />
      <created>1755248421472</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755248421472</updated>
    </task>
    <task id="LOCAL-00005" summary="验证素材时长">
      <option name="closed" value="true" />
      <created>1755250421837</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755250421837</updated>
    </task>
    <task id="LOCAL-00006" summary="修改时长验证bug">
      <option name="closed" value="true" />
      <created>1755255178327</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755255178327</updated>
    </task>
    <task id="LOCAL-00007" summary="导出时，草稿路径存在就覆盖">
      <option name="closed" value="true" />
      <created>1755255826377</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755255826377</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="第一次提交" />
    <MESSAGE value="mcp基本完成" />
    <MESSAGE value="sse" />
    <MESSAGE value="修改端口以及导出路径问题" />
    <MESSAGE value="验证素材时长" />
    <MESSAGE value="修改时长验证bug" />
    <MESSAGE value="导出时，草稿路径存在就覆盖" />
    <option name="LAST_COMMIT_MESSAGE" value="导出时，草稿路径存在就覆盖" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/jianying_mcp$fastapi_server.coverage" NAME="fastapi_server 覆盖结果" MODIFIED="1755510189861" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
    <SUITE FILE_PATH="coverage/jianying_mcp$export.coverage" NAME="export 覆盖结果" MODIFIED="1755247473735" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft/jianying" />
    <SUITE FILE_PATH="coverage/jianying_mcp$server.coverage" NAME="server 覆盖结果" MODIFIED="1755510181530" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
    <SUITE FILE_PATH="coverage/jianying_mcp$debug.coverage" NAME="debug 覆盖结果" MODIFIED="1755155797257" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/jianyingdraft" />
  </component>
</project>